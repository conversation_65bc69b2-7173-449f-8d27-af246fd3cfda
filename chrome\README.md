# Chrome本地存储管理插件

一个功能强大的Chrome插件，用于管理本地存储、支持用户登录和数据同步功能。

## 功能特性

### 1. 本地存储读取功能
- 根据key读取Chrome本地存储的值
- 支持读取当前页面的localStorage
- 实时显示存储的键值对列表

### 2. 用户登录功能
- 安全的用户认证系统
- 登录状态持久化保存
- 支持退出登录功能

### 3. 数据同步功能
- 登录后从服务器接口获取数据列表
- 根据服务器数据自动更新本地存储
- 同步完成后自动刷新当前页面
- 实时显示同步状态和结果

### 4. 现代化界面
- 使用Vue2框架开发
- 响应式设计，适配不同屏幕尺寸
- 直观的用户界面和良好的交互体验

## 安装方法

### 开发者模式安装

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹（包含manifest.json的文件夹）
6. 插件安装完成，可在浏览器工具栏看到插件图标

## 使用说明

### 登录功能
1. 点击浏览器工具栏中的插件图标
2. 在登录界面输入用户名和密码
   - 测试账号：用户名 `admin`，密码 `123456`
3. 点击"登录"按钮
4. 登录成功后会自动跳转到主功能界面

### 本地存储读取
1. 在"读取本地存储"区域输入要查询的key
2. 点击"获取值"按钮
3. 系统会显示对应的存储值

### 数据同步
1. 登录成功后，点击"从服务器同步数据"按钮
2. 系统会从服务器获取最新的配置数据
3. 自动更新本地存储并刷新当前页面
4. 同步结果会在界面上显示

### 存储数据查看
- 登录后可以在"当前存储数据"区域查看所有已保存的键值对
- 数据会实时更新显示

## 文件结构

```
chrome/
├── manifest.json          # 插件配置文件
├── popup.html            # 弹出窗口HTML
├── popup.css             # 样式文件
├── popup.js              # Vue2主逻辑文件
├── api.js                # API服务模块
├── background.js         # 后台脚本
├── content.js            # 内容脚本
└── README.md             # 说明文档
```

## 技术栈

- **前端框架**: Vue.js 2.6.14
- **样式**: 原生CSS3
- **API通信**: Fetch API
- **存储**: Chrome Storage API
- **权限**: Chrome Extensions API

## 开发配置

### API配置
在 `api.js` 文件中修改API配置：

```javascript
this.baseUrl = 'https://your-api-domain.com'; // 替换为实际API地址
this.endpoints = {
    login: '/auth/login',
    logout: '/auth/logout',
    getData: '/data/list',
    // ... 其他端点
};
```

### 测试账号
- 用户名: `admin`
- 密码: `123456`

## 权限说明

插件需要以下权限：
- `storage`: 访问Chrome本地存储
- `activeTab`: 访问当前活动标签页
- `tabs`: 管理浏览器标签页
- `http://*/*` 和 `https://*/*`: 访问所有网站（用于API调用）

## 开发说明

### 模拟API
当前版本使用模拟API进行开发测试。在生产环境中，需要：

1. 在 `api.js` 中配置真实的API地址
2. 修改 `popup.js` 中的API调用方法：
   ```javascript
   // 将模拟调用
   const response = await apiService.mockApiCall('login', { username, password });
   
   // 替换为真实调用
   const response = await apiService.login(username, password);
   ```

### 自定义配置
- 修改 `manifest.json` 中的插件名称、描述等信息
- 在 `popup.css` 中自定义界面样式
- 在 `popup.js` 中添加新的功能逻辑

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查manifest.json语法是否正确
   - 确保所有引用的文件都存在

2. **登录失败**
   - 检查网络连接
   - 确认API地址配置正确
   - 查看浏览器控制台错误信息

3. **数据同步失败**
   - 确认已正确登录
   - 检查API接口是否正常
   - 查看Chrome开发者工具中的错误信息

### 调试方法
1. 右键点击插件图标，选择"检查弹出内容"
2. 在开发者工具中查看控制台日志
3. 检查Network标签页中的API请求

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的本地存储读取功能
- 添加用户登录和数据同步功能
- 使用Vue2框架构建用户界面

## 许可证

本项目仅供学习和开发使用。
