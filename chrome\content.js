// Content Script - 在网页中运行的脚本

console.log('本地存储管理插件 Content Script 已加载');

// 监听来自popup或background的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Content Script 收到消息:', request);
    
    switch (request.action) {
        case 'getLocalStorage':
            handleGetLocalStorage(request.key, sendResponse);
            return true;
            
        case 'setLocalStorage':
            handleSetLocalStorage(request.key, request.value, sendResponse);
            return true;
            
        case 'getAllLocalStorage':
            handleGetAllLocalStorage(sendResponse);
            return true;
            
        case 'clearLocalStorage':
            handleClearLocalStorage(request.keys, sendResponse);
            return true;
            
        case 'getPageInfo':
            handleGetPageInfo(sendResponse);
            return true;
            
        default:
            sendResponse({ success: false, message: '未知的操作' });
    }
});

// 获取localStorage中的值
function handleGetLocalStorage(key, sendResponse) {
    try {
        const value = localStorage.getItem(key);
        sendResponse({ 
            success: true, 
            data: value,
            key: key
        });
    } catch (error) {
        console.error('获取localStorage失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 设置localStorage中的值
function handleSetLocalStorage(key, value, sendResponse) {
    try {
        localStorage.setItem(key, value);
        sendResponse({ 
            success: true, 
            message: '设置成功',
            key: key,
            value: value
        });
    } catch (error) {
        console.error('设置localStorage失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 获取所有localStorage数据
function handleGetAllLocalStorage(sendResponse) {
    try {
        const allData = {};
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            allData[key] = localStorage.getItem(key);
        }
        
        sendResponse({ 
            success: true, 
            data: allData,
            count: localStorage.length
        });
    } catch (error) {
        console.error('获取所有localStorage失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 清除指定的localStorage项
function handleClearLocalStorage(keys, sendResponse) {
    try {
        if (Array.isArray(keys)) {
            keys.forEach(key => localStorage.removeItem(key));
        } else if (typeof keys === 'string') {
            localStorage.removeItem(keys);
        } else {
            localStorage.clear();
        }
        
        sendResponse({ 
            success: true, 
            message: '清除成功'
        });
    } catch (error) {
        console.error('清除localStorage失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 获取页面信息
function handleGetPageInfo(sendResponse) {
    try {
        const pageInfo = {
            url: window.location.href,
            title: document.title,
            domain: window.location.hostname,
            protocol: window.location.protocol,
            pathname: window.location.pathname,
            search: window.location.search,
            hash: window.location.hash,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };
        
        sendResponse({ 
            success: true, 
            data: pageInfo
        });
    } catch (error) {
        console.error('获取页面信息失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 工具函数：向background script发送消息
function sendMessageToBackground(message) {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage(message, (response) => {
            resolve(response);
        });
    });
}

// 监听页面的localStorage变化（如果需要）
function observeLocalStorageChanges() {
    const originalSetItem = localStorage.setItem;
    const originalRemoveItem = localStorage.removeItem;
    const originalClear = localStorage.clear;
    
    localStorage.setItem = function(key, value) {
        const result = originalSetItem.apply(this, arguments);
        console.log('localStorage设置:', key, value);
        
        // 可以在这里通知background script
        sendMessageToBackground({
            action: 'localStorageChanged',
            type: 'set',
            key: key,
            value: value,
            url: window.location.href
        });
        
        return result;
    };
    
    localStorage.removeItem = function(key) {
        const result = originalRemoveItem.apply(this, arguments);
        console.log('localStorage删除:', key);
        
        sendMessageToBackground({
            action: 'localStorageChanged',
            type: 'remove',
            key: key,
            url: window.location.href
        });
        
        return result;
    };
    
    localStorage.clear = function() {
        const result = originalClear.apply(this, arguments);
        console.log('localStorage清空');
        
        sendMessageToBackground({
            action: 'localStorageChanged',
            type: 'clear',
            url: window.location.href
        });
        
        return result;
    };
}

// 初始化
function init() {
    // 如果需要监听localStorage变化，取消下面的注释
    // observeLocalStorageChanges();
    
    console.log('Content Script 初始化完成');
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}
