// API服务模块 - 处理与服务器的通信

class ApiService {
    constructor() {
        // 配置API基础信息
        this.baseUrl = 'https://api.example.com'; // 请替换为实际的API地址
        this.endpoints = {
            login: '/auth/login',
            logout: '/auth/logout',
            getData: '/data/list',
            updateData: '/data/update',
            userInfo: '/user/info'
        };
        
        // 请求超时时间（毫秒）
        this.timeout = 10000;
    }
    
    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = this.baseUrl + endpoint;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: this.timeout
        };
        
        const config = { ...defaultOptions, ...options };
        
        // 添加认证token
        const token = await this.getAuthToken();
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), config.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return { success: true, data };
            
        } catch (error) {
            console.error('API请求失败:', error);
            
            if (error.name === 'AbortError') {
                return { success: false, message: '请求超时' };
            }
            
            return { 
                success: false, 
                message: error.message || '网络请求失败' 
            };
        }
    }
    
    // 用户登录
    async login(username, password) {
        try {
            const response = await this.request(this.endpoints.login, {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });
            
            if (response.success && response.data.token) {
                // 保存认证信息
                await chrome.storage.local.set({
                    authToken: response.data.token,
                    userInfo: response.data.user,
                    loginTime: new Date().toISOString()
                });
                
                return {
                    success: true,
                    user: response.data.user,
                    token: response.data.token
                };
            }
            
            return response;
            
        } catch (error) {
            console.error('登录失败:', error);
            return { 
                success: false, 
                message: '登录失败: ' + error.message 
            };
        }
    }
    
    // 用户退出登录
    async logout() {
        try {
            // 调用服务器退出登录接口
            await this.request(this.endpoints.logout, {
                method: 'POST'
            });
            
            // 清除本地认证信息
            await chrome.storage.local.remove(['authToken', 'userInfo', 'loginTime']);
            
            return { success: true, message: '退出登录成功' };
            
        } catch (error) {
            console.error('退出登录失败:', error);
            
            // 即使服务器请求失败，也要清除本地信息
            await chrome.storage.local.remove(['authToken', 'userInfo', 'loginTime']);
            
            return { 
                success: false, 
                message: '退出登录失败: ' + error.message 
            };
        }
    }
    
    // 获取数据列表
    async getDataList() {
        try {
            const response = await this.request(this.endpoints.getData);
            
            if (response.success) {
                return {
                    success: true,
                    data: response.data.list || response.data,
                    total: response.data.total || (response.data.list ? response.data.list.length : 0)
                };
            }
            
            return response;
            
        } catch (error) {
            console.error('获取数据列表失败:', error);
            return { 
                success: false, 
                message: '获取数据失败: ' + error.message 
            };
        }
    }
    
    // 更新数据
    async updateData(dataList) {
        try {
            const response = await this.request(this.endpoints.updateData, {
                method: 'POST',
                body: JSON.stringify({ data: dataList })
            });
            
            return response;
            
        } catch (error) {
            console.error('更新数据失败:', error);
            return { 
                success: false, 
                message: '更新数据失败: ' + error.message 
            };
        }
    }
    
    // 获取用户信息
    async getUserInfo() {
        try {
            const response = await this.request(this.endpoints.userInfo);
            return response;
            
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return { 
                success: false, 
                message: '获取用户信息失败: ' + error.message 
            };
        }
    }
    
    // 获取认证token
    async getAuthToken() {
        try {
            const result = await chrome.storage.local.get(['authToken']);
            return result.authToken || null;
        } catch (error) {
            console.error('获取认证token失败:', error);
            return null;
        }
    }
    
    // 检查token是否有效
    async isTokenValid() {
        try {
            const token = await this.getAuthToken();
            if (!token) return false;
            
            // 可以通过调用用户信息接口来验证token
            const response = await this.getUserInfo();
            return response.success;
            
        } catch (error) {
            console.error('验证token失败:', error);
            return false;
        }
    }
    
    // 模拟API调用（用于开发测试）
    async mockApiCall(endpoint, data = {}) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 800));
        
        switch (endpoint) {
            case 'login':
                if (data.username === 'admin' && data.password === '123456') {
                    return {
                        success: true,
                        data: {
                            user: {
                                id: 1,
                                username: data.username,
                                name: '管理员',
                                email: '<EMAIL>'
                            },
                            token: 'mock_token_' + Date.now()
                        }
                    };
                } else {
                    return {
                        success: false,
                        message: '用户名或密码错误'
                    };
                }
                
            case 'getData':
                return {
                    success: true,
                    data: {
                        list: [
                            { key: 'theme', value: 'dark', description: '主题设置' },
                            { key: 'language', value: 'zh-CN', description: '语言设置' },
                            { key: 'autoSave', value: true, description: '自动保存' },
                            { key: 'notifications', value: false, description: '通知设置' },
                            { key: 'fontSize', value: '14px', description: '字体大小' },
                            { key: 'lastSync', value: new Date().toISOString(), description: '最后同步时间' }
                        ],
                        total: 6
                    }
                };
                
            case 'userInfo':
                return {
                    success: true,
                    data: {
                        id: 1,
                        username: 'admin',
                        name: '管理员',
                        email: '<EMAIL>',
                        lastLogin: new Date().toISOString()
                    }
                };
                
            default:
                return {
                    success: false,
                    message: '未知的API端点'
                };
        }
    }
}

// 创建API服务实例
const apiService = new ApiService();

// 导出API服务（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiService;
}
