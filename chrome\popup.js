// Vue2应用实例
new Vue({
    el: '#app',
    data: {
        // 登录状态
        isLoggedIn: false,
        isLoading: false,
        loginError: '',
        loginForm: {
            username: '',
            password: ''
        },
        
        // 本地存储功能
        storageKey: '',
        storageValue: null,
        storageList: [],
        
        // 数据同步功能
        isSyncing: false,
        syncMessage: '',
        syncMessageType: '',
        
        // 用户信息
        userInfo: null,
        
        // API配置
        apiConfig: {
            baseUrl: 'https://api.example.com', // 请替换为实际的API地址
            loginEndpoint: '/auth/login',
            dataEndpoint: '/data/list'
        }
    },
    
    mounted() {
        this.checkLoginStatus();
        this.loadStorageList();
    },
    
    methods: {
        // 检查登录状态
        async checkLoginStatus() {
            try {
                const result = await chrome.storage.local.get(['userInfo', 'authToken']);
                if (result.userInfo && result.authToken) {
                    this.isLoggedIn = true;
                    this.userInfo = result.userInfo;
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
            }
        },
        
        // 用户登录
        async login() {
            if (!this.loginForm.username || !this.loginForm.password) {
                this.loginError = '请输入用户名和密码';
                return;
            }
            
            this.isLoading = true;
            this.loginError = '';
            
            try {
                // 使用API服务进行登录
                const response = await this.apiLogin(
                    this.loginForm.username,
                    this.loginForm.password
                );

                if (response.success) {
                    // 保存用户信息和token到Chrome存储
                    await chrome.storage.local.set({
                        userInfo: response.data ? response.data.user : response.user,
                        authToken: response.data ? response.data.token : response.token
                    });

                    this.userInfo = response.data ? response.data.user : response.user;
                    this.isLoggedIn = true;
                    this.loginForm = { username: '', password: '' };

                    // 登录成功后自动同步数据
                    setTimeout(() => {
                        this.syncData();
                    }, 500);
                } else {
                    this.loginError = response.message || '登录失败';
                }
            } catch (error) {
                console.error('登录失败:', error);
                this.loginError = '登录失败，请检查网络连接';
            } finally {
                this.isLoading = false;
            }
        },
        
        // 用户退出登录
        async logout() {
            try {
                await chrome.storage.local.remove(['userInfo', 'authToken']);
                this.isLoggedIn = false;
                this.userInfo = null;
                this.storageList = [];
                this.storageValue = null;
                this.storageKey = '';
                this.syncMessage = '';
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        },
        
        // 获取本地存储值
        async getStorageValue() {
            if (!this.storageKey.trim()) {
                alert('请输入要查询的key');
                return;
            }
            
            try {
                // 首先尝试从Chrome存储中获取
                const chromeResult = await chrome.storage.local.get([this.storageKey]);
                if (chromeResult[this.storageKey] !== undefined) {
                    this.storageValue = JSON.stringify(chromeResult[this.storageKey]);
                    return;
                }
                
                // 如果Chrome存储中没有，尝试从当前页面的localStorage获取
                const tabs = await chrome.tabs.query({active: true, currentWindow: true});
                if (tabs[0]) {
                    const results = await chrome.scripting.executeScript({
                        target: {tabId: tabs[0].id},
                        function: (key) => {
                            return localStorage.getItem(key);
                        },
                        args: [this.storageKey]
                    });
                    
                    if (results && results[0] && results[0].result !== null) {
                        this.storageValue = results[0].result;
                    } else {
                        this.storageValue = '未找到该key对应的值';
                    }
                }
            } catch (error) {
                console.error('获取存储值失败:', error);
                this.storageValue = '获取失败: ' + error.message;
            }
        },
        
        // 从服务器同步数据
        async syncData() {
            if (!this.isLoggedIn) {
                return;
            }
            
            this.isSyncing = true;
            this.syncMessage = '';
            
            try {
                const authToken = await this.getAuthToken();
                if (!authToken) {
                    throw new Error('未找到认证token');
                }

                // 使用API服务获取数据列表
                const response = await this.apiGetData();

                if (response.success) {
                    const dataList = response.data.list || response.data;

                    if (dataList && Array.isArray(dataList)) {
                        // 根据服务器返回的数据更新本地存储
                        const updatePromises = dataList.map(async (item) => {
                            if (item.key && item.value !== undefined) {
                                await chrome.storage.local.set({
                                    [item.key]: item.value
                                });
                            }
                        });

                        await Promise.all(updatePromises);

                        // 刷新存储列表显示
                        await this.loadStorageList();

                        // 刷新当前页面
                        const tabs = await chrome.tabs.query({active: true, currentWindow: true});
                        if (tabs[0]) {
                            chrome.tabs.reload(tabs[0].id);
                        }

                        this.syncMessage = `成功同步 ${dataList.length} 条数据`;
                        this.syncMessageType = 'success';
                    } else {
                        throw new Error('服务器返回的数据格式不正确');
                    }
                } else {
                    throw new Error(response.message || '同步失败');
                }
            } catch (error) {
                console.error('数据同步失败:', error);
                this.syncMessage = '同步失败: ' + error.message;
                this.syncMessageType = 'error';
            } finally {
                this.isSyncing = false;
                
                // 3秒后清除消息
                setTimeout(() => {
                    this.syncMessage = '';
                }, 3000);
            }
        },
        
        // 加载存储列表用于显示
        async loadStorageList() {
            try {
                const allData = await chrome.storage.local.get(null);
                this.storageList = Object.entries(allData)
                    .filter(([key]) => !['userInfo', 'authToken'].includes(key))
                    .map(([key, value]) => ({
                        key,
                        value: typeof value === 'object' ? JSON.stringify(value) : String(value)
                    }));
            } catch (error) {
                console.error('加载存储列表失败:', error);
            }
        },
        
        // 获取认证token
        async getAuthToken() {
            try {
                const result = await chrome.storage.local.get(['authToken']);
                return result.authToken;
            } catch (error) {
                console.error('获取token失败:', error);
                return null;
            }
        },
        
        // 使用API服务进行登录
        async apiLogin(username, password) {
            try {
                // 在实际环境中，使用真实API
                // const response = await apiService.login(username, password);

                // 开发阶段使用模拟API
                const response = await apiService.mockApiCall('login', { username, password });
                return response;
            } catch (error) {
                console.error('API登录失败:', error);
                return { success: false, message: '登录失败: ' + error.message };
            }
        },

        // 使用API服务获取数据
        async apiGetData() {
            try {
                // 在实际环境中，使用真实API
                // const response = await apiService.getDataList();

                // 开发阶段使用模拟API
                const response = await apiService.mockApiCall('getData');
                return response;
            } catch (error) {
                console.error('API获取数据失败:', error);
                return { success: false, message: '获取数据失败: ' + error.message };
            }
        }
    }
});
