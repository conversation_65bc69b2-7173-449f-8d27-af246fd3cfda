body {
    width: 400px;
    min-height: 300px;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow-x: hidden;
}

#app {
    padding: 20px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-container h2 {
    color: #333;
    margin-bottom: 25px;
    font-weight: 600;
    font-size: 24px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.main-container {
    max-height: 500px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.4s ease-out;
}

.main-container::-webkit-scrollbar {
    width: 6px;
}

.main-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.main-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;
}

.main-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.header h2 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.section {
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 12px 12px 0 0;
}

.section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.btn, .login-btn, .logout-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before, .login-btn::before, .logout-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before, .login-btn:hover::before, .logout-btn:hover::before {
    width: 300px;
    height: 300px;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 12px;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.login-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.login-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.logout-btn {
    background-color: #f44336;
    color: white;
    font-size: 12px;
    padding: 6px 12px;
}

.logout-btn:hover {
    background-color: #da190b;
}

.btn {
    background-color: #2196F3;
    color: white;
    margin-left: 10px;
}

.btn:hover:not(:disabled) {
    background-color: #1976D2;
}

.btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.sync-btn {
    width: 100%;
    margin-left: 0;
    background-color: #FF9800;
}

.sync-btn:hover:not(:disabled) {
    background-color: #F57C00;
}

.result, .message {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
}

.result {
    background-color: #e8f5e8;
    border: 1px solid #4CAF50;
    color: #2e7d32;
}

.message.success {
    background-color: #e8f5e8;
    border: 1px solid #4CAF50;
    color: #2e7d32;
}

.message.error {
    background-color: #ffebee;
    border: 1px solid #f44336;
    color: #c62828;
}

.error-message {
    margin-top: 10px;
    padding: 10px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    color: #c62828;
    border-radius: 4px;
    font-size: 14px;
}

.storage-list {
    max-height: 200px;
    overflow-y: auto;
}

.storage-item {
    padding: 8px;
    margin-bottom: 5px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border-left: 3px solid #2196F3;
    font-size: 13px;
    word-break: break-all;
}

.storage-item:last-child {
    margin-bottom: 0;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 成功状态动画 */
.success-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    position: relative;
}

.success-icon::after {
    content: '✓';
    color: #4CAF50;
    font-weight: bold;
    animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 错误状态动画 */
.error-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    position: relative;
}

.error-icon::after {
    content: '✕';
    color: #f44336;
    font-weight: bold;
    animation: errormark 0.5s ease-in-out;
}

@keyframes errormark {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* 脉冲动画 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* 响应式设计 */
@media (max-width: 450px) {
    body {
        width: 350px;
    }

    #app {
        padding: 15px;
    }

    .section {
        padding: 15px;
    }

    .login-container {
        padding: 25px 20px;
    }
}
