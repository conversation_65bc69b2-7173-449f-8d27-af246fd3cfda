<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地存储管理</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div id="app">
        <!-- 登录界面 -->
        <div v-if="!isLoggedIn" class="login-container">
            <h2>用户登录</h2>
            <form @submit.prevent="login">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input 
                        type="text" 
                        id="username" 
                        v-model="loginForm.username" 
                        required
                        placeholder="请输入用户名"
                    >
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input 
                        type="password" 
                        id="password" 
                        v-model="loginForm.password" 
                        required
                        placeholder="请输入密码"
                    >
                </div>
                <button type="submit" :disabled="isLoading" class="login-btn">
                    <span v-if="isLoading" class="loading-spinner"></span>
                    {{ isLoading ? '登录中...' : '登录' }}
                </button>
            </form>
            <div v-if="loginError" class="error-message">
                {{ loginError }}
            </div>
        </div>

        <!-- 主功能界面 -->
        <div v-else class="main-container">
            <div class="header">
                <h2>本地存储管理</h2>
                <button @click="logout" class="logout-btn">退出登录</button>
            </div>

            <!-- 本地存储读取功能 -->
            <div class="section">
                <h3>读取本地存储</h3>
                <div class="form-group">
                    <label for="storageKey">存储Key:</label>
                    <input 
                        type="text" 
                        id="storageKey" 
                        v-model="storageKey" 
                        placeholder="请输入要查询的key"
                    >
                    <button @click="getStorageValue" class="btn">获取值</button>
                </div>
                <div v-if="storageValue !== null" class="result">
                    <strong>值:</strong> {{ storageValue }}
                </div>
            </div>

            <!-- 数据同步功能 -->
            <div class="section">
                <h3>数据同步</h3>
                <button @click="syncData" :disabled="isSyncing" class="btn sync-btn">
                    <span v-if="isSyncing" class="loading-spinner"></span>
                    {{ isSyncing ? '同步中...' : '从服务器同步数据' }}
                </button>
                <div v-if="syncMessage" class="message" :class="syncMessageType">
                    {{ syncMessage }}
                </div>
            </div>

            <!-- 存储列表显示 -->
            <div class="section" v-if="storageList.length > 0">
                <h3>当前存储数据</h3>
                <div class="storage-list">
                    <div v-for="item in storageList" :key="item.key" class="storage-item">
                        <strong>{{ item.key }}:</strong> {{ item.value }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue2 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="api.js"></script>
    <script src="popup.js"></script>
</body>
</html>
