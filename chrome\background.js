// Chrome插件后台脚本

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('插件已安装/更新:', details.reason);
    
    // 设置默认配置
    chrome.storage.local.set({
        'plugin_version': '1.0.0',
        'install_time': new Date().toISOString()
    });
});

// 监听来自popup或content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);
    
    switch (request.action) {
        case 'getStorageData':
            handleGetStorageData(request.key, sendResponse);
            return true; // 保持消息通道开放
            
        case 'setStorageData':
            handleSetStorageData(request.key, request.value, sendResponse);
            return true;
            
        case 'syncDataFromServer':
            handleSyncDataFromServer(request.token, sendResponse);
            return true;
            
        case 'refreshCurrentTab':
            handleRefreshCurrentTab(sendResponse);
            return true;
            
        default:
            sendResponse({ success: false, message: '未知的操作' });
    }
});

// 处理获取存储数据
async function handleGetStorageData(key, sendResponse) {
    try {
        if (key) {
            // 获取特定key的值
            const result = await chrome.storage.local.get([key]);
            sendResponse({ 
                success: true, 
                data: result[key] || null 
            });
        } else {
            // 获取所有数据
            const allData = await chrome.storage.local.get(null);
            sendResponse({ 
                success: true, 
                data: allData 
            });
        }
    } catch (error) {
        console.error('获取存储数据失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 处理设置存储数据
async function handleSetStorageData(key, value, sendResponse) {
    try {
        await chrome.storage.local.set({ [key]: value });
        sendResponse({ 
            success: true, 
            message: '数据保存成功' 
        });
    } catch (error) {
        console.error('设置存储数据失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 处理从服务器同步数据
async function handleSyncDataFromServer(token, sendResponse) {
    try {
        // 这里应该是实际的API调用
        // 现在使用模拟数据
        const mockServerData = [
            { key: 'theme', value: 'dark' },
            { key: 'language', value: 'zh-CN' },
            { key: 'autoSave', value: true },
            { key: 'notifications', value: false },
            { key: 'lastSync', value: new Date().toISOString() }
        ];
        
        // 批量更新本地存储
        const updateData = {};
        mockServerData.forEach(item => {
            updateData[item.key] = item.value;
        });
        
        await chrome.storage.local.set(updateData);
        
        sendResponse({ 
            success: true, 
            data: mockServerData,
            message: `成功同步 ${mockServerData.length} 条数据`
        });
    } catch (error) {
        console.error('同步数据失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 处理刷新当前标签页
async function handleRefreshCurrentTab(sendResponse) {
    try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs[0]) {
            await chrome.tabs.reload(tabs[0].id);
            sendResponse({ 
                success: true, 
                message: '页面刷新成功' 
            });
        } else {
            sendResponse({ 
                success: false, 
                message: '未找到活动标签页' 
            });
        }
    } catch (error) {
        console.error('刷新页面失败:', error);
        sendResponse({ 
            success: false, 
            message: error.message 
        });
    }
}

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    console.log('存储发生变化:', changes, 'namespace:', namespace);
    
    // 可以在这里添加存储变化的处理逻辑
    // 比如通知content script或popup
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        console.log('标签页加载完成:', tab.url);
        // 可以在这里添加页面加载完成后的处理逻辑
    }
});

// 工具函数：发送消息到content script
async function sendMessageToContentScript(tabId, message) {
    try {
        const response = await chrome.tabs.sendMessage(tabId, message);
        return response;
    } catch (error) {
        console.error('发送消息到content script失败:', error);
        return { success: false, message: error.message };
    }
}

// 工具函数：获取当前活动标签页
async function getCurrentTab() {
    try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        return tabs[0] || null;
    } catch (error) {
        console.error('获取当前标签页失败:', error);
        return null;
    }
}
